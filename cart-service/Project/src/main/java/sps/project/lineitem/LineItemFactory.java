package sps.project.lineitem;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import sps.common.exception.CommerceErrorCode;
import sps.common.exception.RestletException;
import sps.common.logging.Splunk;
import sps.common.mongo.CartServiceOidDispenser;
import sps.common.restlet.RequestSettings;
import sps.order.OrderPricingHelper;
import sps.resource.interfaces.project.ProjectLineItemResource;
import sps.resource.pojo.metadata.AttributeType;
import sps.resource.pojo.metadata.Metadata;
import sps.resource.pojo.order.LineItem;
import sps.resource.pojo.order.PriceOperation;
import sps.resource.pojo.pricing.LineItemOption;
import sps.resource.pojo.project.Project;
import sps.resource.pojo.project.Project.SurfaceCategory;
import sps.resource.pojo.project.metadata.ProjectMetadata;
import sps.resource.pojo.sku.AbstractProduct;
import sps.resource.pojo.sku.DeliveryMethod;
import sps.resource.pojo.sku.DeliveryOption;
import sps.resource.pojo.sku.Option;
import sps.resource.pojo.sku.Sku;
import sps.sku.OptionFactory;
import sps.sku.SkuFactory;

/**
 * © Copyright 2013 Snapfish LLC
 */
public class LineItemFactory {

	private static final Splunk SPLUNK = Splunk.getInstance("cart-service", LineItemFactory.class);
	static final String LINEITEM_OID_COLLECTION_NAME = "lineItem";
	static final String LINEITEMOPTION_OID_COLLECTION_NAME = "lineItemOption";
	// I don't like this queryParams thing
	private final Map<String, String> queryParams;

	public LineItemFactory(Map<String, String> queryParams) {
		this.queryParams = queryParams;
	}

	public LineItemFactory() {
		this.queryParams = new HashMap<String, String>();
	}

	public LineItem getInstance(RequestSettings settings, int qty, String accountId, String context, Project project) {
		// TODO: The entire project lineitem code should be redesigned and refactored
		// Note: this method always called getInstance with doPrice=true.
		// Note: changed it to check the query parameter 'price' to override.
		// Note: other places call getInstance with false, must leave as is -- very
		// convoluted logic. See TODO above
		String stringPrice = getQueryParam("price");
		boolean doPrice = true; // default to calling price
		boolean throwPriceException = false; // only throw exception if called with doPrice = true
		if (stringPrice != null) {
			if ("true".equalsIgnoreCase(stringPrice) || "t".equalsIgnoreCase(stringPrice) || "1".equals(stringPrice)) {
				throwPriceException = true;
			} else if ("false".equalsIgnoreCase(stringPrice) || "f".equalsIgnoreCase(stringPrice)
					|| "0".equals(stringPrice)) {
				doPrice = false;
			}
		}

		return getInstance(settings, qty, accountId, context, project, doPrice, throwPriceException);
	}

	public LineItem getInstance(RequestSettings settings, int qty, String accountId, String context, Project project,
			boolean doPrice, boolean throwPriceException) {
		String overrideSku = getQueryParam(ProjectLineItemResource.SKU);
		LineItem lineItem = createLineItem(settings, project, qty, overrideSku);
		
		// Will not compute taxes if not provided
		String shippingAddressId = getQueryParam("shippingAddressId");
		// Default to MailOrder
		String stringDeliveryMethod = getQueryParam("deliveryMethod");
		DeliveryMethod deliveryMethod = stringDeliveryMethod == null ? DeliveryMethod.MailOrder
				: DeliveryMethod.valueOf(stringDeliveryMethod);
		// Default to Standard
		String stringDeliveryOption = getQueryParam("deliveryOption");
		//        DeliveryOption deliveryOption =
		// stringDeliveryOption == null ? DeliveryOption.Standard :
		// DeliveryOption.valueOf(stringDeliveryOption);
		DeliveryOption deliveryOption;
		if (stringDeliveryOption == null) {
			if ("/sfly/shutterfly/shutterfly-us/default".equalsIgnoreCase(context)) {
				deliveryOption = DeliveryOption.Economy;
			} else {
				deliveryOption = DeliveryOption.Standard;
			}
		} else {
			deliveryOption = DeliveryOption.valueOf(stringDeliveryOption);
		}

		if (stringDeliveryMethod == null) {
			String sku = overrideSku == null ? project.getCommerceSku() : overrideSku;
			if (!(new SkuFactory().isFulfillable(settings, sku, context))) {
				deliveryMethod = DeliveryMethod.Digital;
				deliveryOption = DeliveryOption.NoShipping;
			}
		}

		// Better be there if RetailPickup
		String retailer = getQueryParam("retailer");

		if (doPrice == false)
			return lineItem;

		try {
			// There could be pricing errors because of bad parameters. We want
			// to at least
			// return the un-priced line item
			lineItem = OrderPricingHelper.priceLineItem(settings, PriceOperation.All, lineItem, context,
					shippingAddressId, deliveryMethod, deliveryOption, retailer, accountId);
		} catch (Exception e) {
			if (throwPriceException)
				throw e;
		}
		return lineItem;
	}

	private LineItem createLineItem(RequestSettings settings, Project project, int qty, String overrideSku) {
		if (project.getProjectType() == null)
			return null;
		Sku sku = getSku(settings, project, overrideSku);
		return getLineItems(settings, project, qty, sku);
	}

	private int computeExtraPageQty(int surfaces, int basePages, int pageIncrement) {
		int extra = (surfaces - basePages) < 0 ? 0 : surfaces - basePages;
		return extra / pageIncrement;
	}

	private Boolean hasAdditionalSurfaces(Sku sku) {
		Map<String, Object> skuAttributes = sku.getAttributes();
		if (skuAttributes.containsKey("additionalSurfaces")
				&& ((Boolean) skuAttributes.get("additionalSurfaces") == Boolean.TRUE)) {

			return Boolean.TRUE;
		}

		return Boolean.FALSE;
	}

	private LineItem getLineItems(RequestSettings settings, Project project, int qty, Sku sku) {
		long lineItemId = CartServiceOidDispenser.sGetNextOid(settings, LINEITEM_OID_COLLECTION_NAME);
		LineItem lineItem = new LineItem(lineItemId, sku.get_id(), project.get_id(), qty, null);
		if (project != null && project.getCommerceSkuQuantity() != null
				&& project.getCommerceSkuQuantity().intValue() > 0) {
			lineItem.setProjectQuantity(qty);
			lineItem.setQuantity(qty * project.getCommerceSkuQuantity().intValue());
		}

		String doNotPriceShippingParam = getQueryParam("doNotPriceShipping");
		boolean doNotPriceShipping = "true".equalsIgnoreCase(doNotPriceShippingParam);

		if (doNotPriceShipping) {
			Metadata metadata = new Metadata();
			metadata.setName("doNotPriceShipping");
			metadata.setValue("true");
			metadata.setMetadataType(AttributeType.Cart);

			Collection<Metadata> lineItemMetadata = lineItem.getMetadata();
			if (lineItemMetadata == null) {
				lineItemMetadata = new ArrayList<>();
			}
			lineItemMetadata.add(metadata);
			lineItem.setMetadata(lineItemMetadata);
		}

		Map<String, Object> skuAttributes = sku.getAttributes();
		if (hasAdditionalSurfaces(sku)) {
		    Collection<Project.SurfaceCategory> categories = project.getSurfaceCategories();
		    int total = 0;
		    for (Project.SurfaceCategory category : categories) {
		        if (category.getCategoryName().equalsIgnoreCase("inside page")
		                || category.getCategoryName().equalsIgnoreCase("inside")) {
		            int totalSurfaces = category.getSurfaces().stream()
		                .mapToInt(surface -> {
		                    boolean isSpreadSurface = surface.getSurfaceMetadata() != null &&
		                        surface.getSurfaceMetadata().stream()
		                            .anyMatch(metadata -> "renderingSurfaceType".equals(metadata.getName())
		                                    && "spread".equals(metadata.getValue()));
		                    return isSpreadSurface ? 2 : 1;
		                })
		                .sum();
		            SPLUNK.log("TotalSurfacesCalculated", Splunk.pair("totalSurfaces", totalSurfaces));
		            total = computeExtraPageQty(totalSurfaces, (int) skuAttributes.get("minimumNumberOfSurfaces"),
		                    (int) skuAttributes.get("additionalSurfacesIncrement"));
		            break;
		        }
		    }
		    if (total > 0) {
		        String optionId = getExtraPagesOptionId(sku);
		        LineItemOption lineItemOption = new LineItemOption(
		                CartServiceOidDispenser.sGetNextOid(settings, LINEITEMOPTION_OID_COLLECTION_NAME), optionId,
		                total * qty);

		        if (doNotPriceShipping) {
		            Metadata metadata = new Metadata();
		            metadata.setName("doNotPriceShipping");
		            metadata.setValue("true");
		            metadata.setMetadataType(AttributeType.Cart);
		            Collection<Metadata> optionMetadata = lineItemOption.getMetadata();
		            if (optionMetadata == null) {
		                optionMetadata = new ArrayList<>();
		            }
		            optionMetadata.add(metadata);
		            lineItemOption.setMetadata(optionMetadata);
		        }

		        lineItem.addLineItemOption(lineItemOption);
		    }
		}

		if (project.getCommerceOptions() != null) {
			for (Project.CommerceOption co : project.getCommerceOptions()) {
				int optionQuantity = (co.getQuantity() == null ? qty : co.getQuantity().intValue());
				if (optionQuantity > 0) {
					LineItemOption lineItemOption = new LineItemOption(
							CartServiceOidDispenser.sGetNextOid(settings, LINEITEMOPTION_OID_COLLECTION_NAME),
							getEnvelopeOptionId(sku, co), optionQuantity);
					SPLUNK.log("BeforeUpdateLineItemOption", Splunk.pair("optionId", lineItemOption.getOptionId()),
							Splunk.pair("quantity", optionQuantity));
					updateLineItemOptionQuantity(project, lineItemOption, optionQuantity, settings);
					SPLUNK.log("AfterUpdateLineItemOption", Splunk.pair("optionId", lineItemOption.getOptionId()),
							Splunk.pair("quantity", lineItemOption.getQuantity()));

					if (doNotPriceShipping) {
						Metadata metadata = new Metadata();
						metadata.setName("doNotPriceShipping");
						metadata.setValue("true");
						metadata.setMetadataType(AttributeType.Cart);
						Collection<Metadata> optionMetadata = lineItemOption.getMetadata();
						if (optionMetadata == null) {
							optionMetadata = new ArrayList<>();
						}
						optionMetadata.add(metadata);
						lineItemOption.setMetadata(optionMetadata);
					}
					lineItem.addLineItemOption(lineItemOption);
				}
			}
		}

		/*
		 * if (project.getSourceGroup() != null && !project.getSourceGroup().isEmpty())
		 * { lineItem.setSourceGroup(project.getSourceGroup()); }
		 */
		if (project.getSaveSourceGroup() != null && project.getSaveSourceGroup().size() >= 1) {
			String sourcegroup = null;
			for (String source : project.getSaveSourceGroup()) {
				sourcegroup = source;
			}
			lineItem.setSourceGroup(sourcegroup);
		}

		// Extract and set designGroupId from project metadata
		setProjectMetadata(lineItem, project);

		return lineItem;
	}

	private Sku getSku(RequestSettings settings, Project project, String overrideSku) {
		String sku = overrideSku == null ? project.getCommerceSku() : overrideSku;
		return new SkuFactory().getInstance(settings, sku, project.getContext());
	}

	private String getExtraPagesOptionId(Sku sku) {
		for (AbstractProduct.CommerceOptionValidity o : sku.getCommerceOptionValidity()) {
			if ("additionalPages".equalsIgnoreCase(o.getOptionCategory())
					|| "ExtraPages".equalsIgnoreCase(o.getOptionCategory()))
				return o.get_id();
		}
		SPLUNK.log("lookup of extraPage optionId for sku failed", Splunk.pair("sku", sku.get_id()));
		throw RestletException.getInstance(CommerceErrorCode.BadRequest,
				"lookup of option id for extra pages failed: sku = " + sku.get_id());
	}

	private String getEnvelopeOptionId(Sku sku, Project.CommerceOption co) {
		for (AbstractProduct.CommerceOptionValidity o : sku.getCommerceOptionValidity()) {
			if (co.getCommerceOption().equals(o.get_id())) {
				return co.getCommerceOption();
			}
		}
		SPLUNK.log("lookup of option id for Cards envelop failed", Splunk.pair("sku", sku.get_id()));
		throw RestletException.getInstance(CommerceErrorCode.BadRequest,
				"lookup of option id for Cards envelop failed: sku = " + sku.get_id());
	}

	private String getQueryParam(String key) {
		return queryParams.get(key);
	}

	private LineItemOption updateLineItemOptionQuantity(Project project, LineItemOption lineItemOption, int quantity,
			RequestSettings settings) {
		if (project != null && project.getCommerceSkuQuantity() != null
				&& project.getCommerceSkuQuantity().intValue() > 0) {
			Option option = new OptionFactory().getInstance(settings, lineItemOption.getOptionId(),
					settings.getContext());
			if (option.getAttributes() != null && option.getAttributes().get("optionQuantity") != null
					&& option.getAttributes().get("optionQuantity").equals("perSku")) {
				lineItemOption.setQuantity(quantity * project.getCommerceSkuQuantity().intValue());
			}
		}
		return lineItemOption;
	}

	/**
	 * Extracts metadata from project and sets it on the line item.
	 * Currently extracts designGroupId from project metadata and adds it to line item metadata.
	 */
	private void setProjectMetadata(LineItem lineItem, Project project) {
		if (project != null && project.getProjectMetadata() != null) {
			for (ProjectMetadata metadata : project.getProjectMetadata()) {
				if ("designGroupId".equals(metadata.getName())) {
					String designGroupId = String.valueOf(metadata.getValue());
					if (designGroupId != null && !designGroupId.trim().isEmpty()) {
						addMetadataToLineItem(lineItem, "designGroupId", designGroupId);
					}
				}
			}
		}
	}

	/**
	 * Adds metadata to line item and its options if they don't already exist.
	 */
	private void addMetadataToLineItem(LineItem lineItem, String key, String value) {
		// Add metadata to line item
		if (lineItem.getMetadata() == null) {
			lineItem.setMetadata(new ArrayList<>());
		}

		if (lineItem.getMetadata().stream().noneMatch(meta -> key.equals(meta.getName()))) {
			Metadata metadataEntry = new Metadata();
			metadataEntry.setName(key);
			metadataEntry.setValue(value);
			metadataEntry.setMetadataType(AttributeType.Cart);
			lineItem.getMetadata().add(metadataEntry);
		}

		// Add metadata to line item options
		if (lineItem.getLineItemOptions() != null) {
			for (LineItemOption option : lineItem.getLineItemOptions()) {
				if (option.getMetadata() == null) {
					option.setMetadata(new ArrayList<>());
				}

				if (option.getMetadata().stream().noneMatch(meta -> key.equals(meta.getName()))) {
					Metadata optionMetadata = new Metadata();
					optionMetadata.setName(key);
					optionMetadata.setValue(value);
					optionMetadata.setMetadataType(AttributeType.Cart);
					option.getMetadata().add(optionMetadata);
				}
			}
		}
	}
}
