package sps.project;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.restlet.data.Parameter;

import sps.common.config.CommonConfig;
import sps.common.discovery.DiscoveryFactory;
import sps.common.exception.CommerceErrorCode;
import sps.common.exception.RestletException;
import sps.common.logging.Splunk;
import sps.common.oauth2.OAuth2Credentials;
import sps.common.oauth2.OAuthCache;
import sps.common.oauth2.TokenGeneratorFactory;
import sps.common.oauth2.http.OAuthHttpClient;
import sps.common.restlet.RequestSettings;
import sps.common.restlet.resource.ResourceClient;
import sps.common.utilities.CommerceObjectMapper;
import sps.common.utilities.GenerateNewS2SToken;
import sps.order.OrderTraverser;
import sps.resource.interfaces.project.ProjectInstanceStateResource;
import sps.resource.interfaces.project.ProjectLineItemResource;
import sps.resource.interfaces.project.ProjectMultiLineItemResource;
import sps.resource.pojo.metadata.AttributeType;
import sps.resource.pojo.metadata.Metadata;
import sps.resource.pojo.order.CatalogDesign;
import sps.resource.pojo.order.LineItem;
import sps.resource.pojo.order.Order;
import sps.resource.pojo.order.ShipBin;
import sps.resource.pojo.pricing.LineItemOption;
import sps.resource.pojo.project.Project;
import sps.resource.pojo.project.Projects;
import sps.resource.pojo.project.Projects.ProjectContainer;
import sps.resource.pojo.project.metadata.ProjectMetadata;
import sps.resource.pojo.sku.DeliveryMethod;
import sps.resource.pojo.sku.Sku;
import sps.sku.SkuFactory;

public class OrderProjectHelper {
    private static final Splunk SPLUNK = Splunk.getInstance("commerce-backend", OrderProjectHelper.class);
    private static final String sflyContext = "/sfly/shutterfly/shutterfly-us/default";

    public Project getProject(RequestSettings settings, LineItem lineItem, String accountId, String context) {
        return new ProjectFactory().getInstance(settings, lineItem.getProjectId(), accountId, context, "fulfillment");
    }

    public static void adjustOrderProjectStates(RequestSettings settings, Order order,
            Project.ProjectOperation operation) {
        Set<String> projectIds = OrderTraverser.getOrderProjectIds(order);
        processProjectStateChanges(settings, order, projectIds, operation);
    }

    public static void adjustUpdatedOrderProjectStates(RequestSettings settings, Order prevOrder, Order updatedOrder, Map<String, Project> projects) {
        Set<String> prevProjectIds = OrderTraverser.getOrderProjectIds(prevOrder);
        Set<String> updatedProjectIds = OrderTraverser.getOrderProjectIds(updatedOrder);

        prevProjectIds.removeAll(updatedProjectIds);
        
        for (Project project: projects.values()) {
            if (project.getState().equals(Project.ProjectState.Bound)) {
                updatedProjectIds.remove(project.get_id());
            }
        }        

        // Unbind any projects no longer in order
        processProjectStateChanges(settings, prevOrder, prevProjectIds, Project.ProjectOperation.unbind);
        // Bind any projects to order
        processProjectStateChanges(settings, updatedOrder, updatedProjectIds, Project.ProjectOperation.bind);
    }

    /*
     * assumes projects don't cross shipbins
     */
    public static void validateProjects(RequestSettings settings, Set<String> projectIds, Order order, Map<String, Project> projects) {
        List<ShipBin> newShipBins =  new ArrayList<ShipBin>();

        for (ShipBin shipBin : order.getShipBins()) {
            Map<String, List<LineItem>> projectToLineItems = new LinkedHashMap<String, List<LineItem>>();
            for (LineItem lineItem : shipBin.getLineItems()) {
                String projectId = lineItem.getProjectId();
                List<LineItem> list = projectToLineItems.get(projectId) == null ? new ArrayList<LineItem>()
                        : projectToLineItems.get(projectId);
                list.add(lineItem);
                projectToLineItems.put(projectId, list);
            }
            List<LineItem> newLineItems = new ArrayList<LineItem>();
            boolean anyProjectModified = false;
            for (String projectId : projectToLineItems.keySet()) {
                if (projectId == null)
                    continue;
                Project project = projects != null ? projects.get(projectId) : null;
                if (project == null) {
                    project = new ProjectFactory().getInstance(settings, projectId, order.getAccountId(), order.getContext(), "fulfillment");                	
                }
                if (project == null)
                    continue;
                
                List<LineItem> clientLineItems = projectToLineItems.get(projectId);
                int quantity = clientLineItems.get(0).getQuantity();
				if (clientLineItems.get(0).getProjectQuantity() != null) {
					quantity = clientLineItems.get(0).getProjectQuantity();
				}
                
                if (quantity == 0) {
                    SPLUNK.error("invalidLineItemQuantity", Splunk.pair("projectId", projectId));  
                }
                boolean isMismatchedLineItemQty = false;
                if (!project.getState().equals(Project.ProjectState.Modified) && quantity != 0) {
                    isMismatchedLineItemQty = isLineItemQtyMismatchWithProjectQty(project, order);
                }
                
                // if the project is modified, or it has an invalid quantity of 0 recreate the lineitem(s)
                if (project.getState().equals(Project.ProjectState.Modified) || quantity == 0 || isMismatchedLineItemQty) {
                    anyProjectModified = true;
                    List<LineItem> lineItems = getNewLineItems(order, shipBin, project, clientLineItems, quantity, settings);
                    if (lineItems != null && lineItems.size() > 0) {
                        newLineItems.addAll(lineItems);
                    }
                } else {
                    Iterable<Sku> skusFromProject = ProjectTraverser.getSkusFrom(settings, project, order.getContext());
                    Set<Sku> skusFromLineItems = getSkus(settings, projectToLineItems.get(projectId), order.getContext());
                    /* ((Set<Sku>)skusFromProject).size() == 0, this condition is to keep fitnesse tests happy,
                    as there are many projects created without sku in fitnesses tests */
                    /*RestletException.affirm(((Set<Sku>)skusFromProject).size() == 0 || skusFromProject.equals(skusFromLineItems),
                            CommerceErrorCode.ProjectLineItemMismatch,
                            "Skus from line items don't match skus from project for project " + projectId);*/
                    if(((Set<Sku>)skusFromProject).size() == 0 || skusFromProject.equals(skusFromLineItems)){
                    	if(settings.getContext().equals(sflyContext))
                    	updateLineItemsWithMissingMetadata(settings, clientLineItems, project);
                        newLineItems.addAll(clientLineItems);                        
                    }else {
                        anyProjectModified = true;
                        List<LineItem> lineItems = getNewLineItems(order, shipBin, project, clientLineItems, quantity, settings);
                        if (lineItems != null && lineItems.size() > 0) {
                            newLineItems.addAll(lineItems);
                        }                        
                    }
                }
            }
            
            SPLUNK.log("Final New Line Items for ShipBin",
                    Splunk.pair("shipBinId", shipBin.getShipBinId()),
                    Splunk.pair("newLineItems", newLineItems));

//            //lineItems with invalid projects will be removed from the order
//            if (anyProjectModified) {
//                if (newLineItems != null && !newLineItems.isEmpty()) {
//                    shipBin.setLineItems(newLineItems);
//                    newShipBins.add(shipBin);
//                }    
//            } else {
//                newShipBins.add(shipBin);
//            }
            if (newLineItems != null && !newLineItems.isEmpty()) {
                shipBin.setLineItems(newLineItems);
            }
            newShipBins.add(shipBin);
        }
        order.setShipBins(newShipBins);

    }

    
    private static boolean isLineItemQtyMismatchWithProjectQty(Project project, Order order) {
        if(!ProjectTraverser.isMultiSkuProject(project))
            return false;
        if(ProjectTraverser.canProjectDuplicateSkus(project))
            return false;
        
        Map<String, Long> orderQtys = new HashMap<String, Long>();
        List<ShipBin> shipBins = order.getShipBins();
        for (ShipBin shipBin : shipBins)
            for (LineItem lineItem : shipBin.getLineItems())
				if (lineItem.getProjectId() != null && lineItem.getProjectId().equals(project.get_id()))
                    orderQtys.put(lineItem.getSkuId(),
                            (orderQtys.containsKey(lineItem.getSkuId())
                                ? orderQtys.get( lineItem.getSkuId())
                                    + lineItem.getQuantity()
                                : lineItem.getQuantity()));

        if (!orderQtys.isEmpty()) {
            Iterator<String> orderProjQtys = orderQtys.keySet().iterator();
            while (orderProjQtys.hasNext()) {
                String projectSkuId = orderProjQtys.next();
                long projectQty = ProjectTraverser.getSkuTotalQuantity(project, projectSkuId);
                if (projectQty > 0
                    && !Long.valueOf(projectQty).toString().equals(orderQtys.get(projectSkuId).toString())) {
                    return true;
                }
            }
        }
        return false;
    }

    private static Set<Sku> getSkus(RequestSettings settings, List<LineItem> list, String context) {
        Set<Sku> skus = new HashSet<>();
        for (LineItem lineItem : list)
            skus.add(new SkuFactory().getInstance(settings, lineItem.getSkuId(), context));
        return skus;
    }

    public static boolean isMultiSkuProject(RequestSettings settings, String projectId, String accountId, String context) {
        Project project = new ProjectFactory().getInstance(settings, projectId, accountId, context, "fulfillment");
        return ProjectTraverser.isMultiSkuProject(project);
    }

    public static boolean isSingleSkuProject(RequestSettings settings, String projectId, String accountId,
            String context) {
        Project project = new ProjectFactory().getInstance(settings, projectId, accountId, context, "fulfillment");
        return ProjectTraverser.isSingleSkuProject(project);
    }

    public boolean isSingleSkuProjectPoly(RequestSettings settings, String projectId, String accountId, String context) {
        return isSingleSkuProject(settings, projectId, accountId, context);
    }

    public static boolean isSingleSkuProjectLineItem(RequestSettings settings, LineItem lineItem, String accountId,
            String context) {
        RestletException.affirm(lineItem != null && lineItem.getProjectId() != null && accountId != null
                && context != null, CommerceErrorCode.InvalidParameter,
                "null project, line item, account id, or context");
        return isSingleSkuProject(settings, lineItem.getProjectId(), accountId, context);
    }

    public boolean isSingleSkuProjectLineItemPoly(RequestSettings settings, LineItem lineItem, String accountId,
            String context) {
        RestletException.affirm(lineItem != null && lineItem.getProjectId() != null && accountId != null
                && context != null, CommerceErrorCode.InvalidParameter,
                "null project, line item, account id, or context");
        return isSingleSkuProject(settings, lineItem.getProjectId(), accountId, context);
    }

    public static boolean isMultiSkuProjectLineItem(RequestSettings settings, LineItem lineItem, String accountId,
            String context) {
        return !isSingleSkuProjectLineItem(settings, lineItem, accountId, context);
    }

    private static void processProjectStateChanges(RequestSettings settings, Order order, Set<String> projectIds,
            Project.ProjectOperation operation) {
        for (String projectId : projectIds) {
            SPLUNK.log("change-project-state", Splunk.pair("operation", operation),
                    Splunk.pair("order", order.get_id()), Splunk.pair("project", projectId));
            settings.setContext(order.getContext());
            
            boolean isDevelop = CommonConfig.getEnviroment().equals("develop") ? true : false;
            
            if (!isDevelop) {
                DiscoveryFactory discoveryFactory = new DiscoveryFactory();
                String uri = discoveryFactory.getProjectsStateUrl(order.getContext(), order.getDataCenter(), projectId,
                        operation.toString(), order.getAccountId(), order.get_id().toString());
                OAuth2Credentials oa2c = new OAuthCache(order.getContext(), null, null, null, order.getDataCenter())
                        .getOAuthCredentials(false);
                HttpPost httpPost = new HttpPost(uri);
                httpPost.addHeader("Authorization", "OAuth " + oa2c.getAccess_token());
                try {
                    new OAuthHttpClient().doHttpRequest("{}", null, Project.class, httpPost, null);
                } catch (Throwable t) {
                    SPLUNK.error("error updating project state", t, Splunk.pair("projectId", projectId),
                            Splunk.pair("origin", "commerce-backend"));
                    throw t;
                }
            } else {
                ResourceClient<ProjectInstanceStateResource, Project> resource = ResourceClient
                        .getInstance(ProjectInstanceStateResource.class, Project.class, settings);
                Parameter state = new Parameter("state", operation.toString());
                Parameter accountIdParam = new Parameter("accountId", order.getAccountId());
                Parameter orderIdParam = new Parameter("orderId", "" + order.get_id());
                resource.post(projectId, state, accountIdParam, orderIdParam);
            }
        }
    }

    
    
    
    private static void logInvalidProjectError(String projectId, Long orderId) {
        SPLUNK.error("invalidProject", Splunk.pair("projectError", "project has no surgaceCategories/surfaces, it will be removed from order"),
                Splunk.pair("projectId", projectId), Splunk.pair("orderId", orderId));       
    }
    
	public static Map<String, Project> getProjects(ShipBin shipBin, String accountId, String context, String dataCenter) {
		Map<String, Project> projects = new HashMap<>();
		if (CommonConfig.getEnviroment().equals("develop")) {
			return projects;
		}
		if (shipBin == null) {
			return projects;
		}
		Set<String> projectIds = getProjectIds(shipBin);
		return getProjects(projectIds, accountId, context, dataCenter);
	}
    
	public static Map<String, Project> getProjects(Order order) {
		Map<String, Project> projects = new HashMap<>();
		if (CommonConfig.getEnviroment().equals("develop")) {
			return projects;
		}
		if (order == null) {
			return projects;
		}
		Set<String> projectIds = getProjectIds(order);
		return getProjects(projectIds, order.getAccountId(), order.getContext(), order.getDataCenter());
	}
	
	public static Map<String, Project> getProjects(Set<String> projectIds, String accountId, String context, String dataCenter) {
		Map<String, Project> projects = new HashMap<>();
		if (projectIds == null || projectIds.size() == 0)
			return projects;
		DiscoveryFactory discoveryFactory = new DiscoveryFactory();
		String url = discoveryFactory.getBulkProjectsUrl(context, dataCenter,
				accountId, projectIds.size());
		OAuth2Credentials accessCredentials = new OAuthCache(context, null, null, null, null)
				.getOAuthCredentials(false);
		
//		String accessToken = GenerateNewS2SToken.getInstance(null).generateS2SToken();
    	

		try {
			CommerceObjectMapper om = CommerceObjectMapper.getInstance();
			String payload = om.writeValueAsString(projectIds);
			HttpPost httpPost = new HttpPost(url);
			httpPost.addHeader("Authorization", "OAuth " + accessCredentials.getAccess_token());
			httpPost.setEntity(new StringEntity(payload, ContentType.APPLICATION_JSON));
			Projects projectsResponse = new OAuthHttpClient().doHttpRequest(payload, null, Projects.class, httpPost,
					null);

			if (projectsResponse != null) {
				List<ProjectContainer> projectContainerList = projectsResponse.getResources();
				if (projectContainerList != null) {
					for (ProjectContainer projectContainer : projectContainerList) {
						if (projectContainer != null && projectContainer.getResource() != null) {
							projects.put(projectContainer.getResource().get_id(), projectContainer.getResource());
						}
					}
				}
			}
		} catch (Throwable t) {
			SPLUNK.error("error getting projectsInfo", t, Splunk.pair("projectIds", projectIds),
					Splunk.pair("origin", "commerce-backend"));
		}
		return projects;
	}

	public static Set<String> getProjectIds(Order order) {
		Set<String> projectIds = new HashSet<String>();
		if (order == null || order.getShipBins().size() == 0)
			return projectIds;
		for (ShipBin shipBin : order.getShipBins()) {
			for (LineItem li : shipBin.getLineItems()) {
				if (li.getProjectId() != null && !li.getProjectId().isEmpty()) {
					projectIds.add(li.getProjectId());
				}
			}
		}
		return projectIds;
	}
	
	public static Set<String> getProjectIds(ShipBin shipBin) {
		Set<String> projectIds = new HashSet<String>();
		if (shipBin == null || shipBin.getLineItems().size() == 0)
			return projectIds;
		for (LineItem li : shipBin.getLineItems()) {
			if (li.getProjectId() != null && !li.getProjectId().isEmpty()) {
				projectIds.add(li.getProjectId());
			}
		}
		return projectIds;
	}
	
    public static List<LineItem> getNewLineItems(Order order, ShipBin shipBin, Project project,
            List<LineItem> clientLineItems, int quantity, RequestSettings settings) {
        List<LineItem> newLineItems = new ArrayList<LineItem>();
        Map<Long, String> lineItemSpvMap = new HashMap<>();
        Parameter shippingAddressId = new Parameter("shippingAddressId", shipBin.getShippingAddressId());
        Parameter deliveryOption = new Parameter("deliveryOption",
                (shipBin.getDeliveryOption() == null ? null : shipBin.getDeliveryOption().toString()));
        Parameter retailerParameter = new Parameter("retailer", shipBin.getRetailer());

        ArrayList<Parameter> parameters = new ArrayList<Parameter>();
        parameters.add(new Parameter(ProjectLineItemResource.ACCOUNT_ID, order.getAccountId()));
        parameters.add(new Parameter("context", order.getContext()));
        parameters.add(new Parameter("deliveryMethod", shipBin.getDeliveryMethod().toString()));
        parameters.add(new Parameter("price", "false"));
        if (ProjectTraverser.isSingleSkuProject(project) && project.getCommerceSku() != null
                && !project.getCommerceSku().isEmpty()) {
            Parameter quantityParameter = new Parameter("quantity", String.valueOf(quantity == 0 ? 1 : quantity));
            Parameter skuParameter = new Parameter("sku", clientLineItems.get(0).getSkuId());
            ResourceClient<ProjectLineItemResource, LineItem> lineItemResource = ResourceClient
                    .getInstance(ProjectLineItemResource.class, LineItem.class, settings);

            parameters.add(quantityParameter);

            if (shipBin.getDeliveryMethod().equals(DeliveryMethod.RetailPickup)) {
                parameters.add(skuParameter);
                parameters.add(retailerParameter);
            } else if (shipBin.getDeliveryMethod().equals(DeliveryMethod.ShipToStore)) {
                parameters.add(retailerParameter);
            } else {
                parameters.add(shippingAddressId);
                parameters.add(deliveryOption);
            }

            LineItem li = lineItemResource.get(project.get_id(), parameters.toArray(new Parameter[parameters.size()]));

            if (li != null){
                li.setMetadata(clientLineItems.get(0).getMetadata());
                if(settings.getContext().equals(sflyContext))
                setProjectSpvData(li, project, lineItemSpvMap, settings);
                newLineItems.add(li);                
            }
            else
                logInvalidProjectError(project.get_id(), order.get_id());
        } else {
            ResourceClient<ProjectMultiLineItemResource, LineItem[]> multiLineItemResource = ResourceClient
                    .getInstance(ProjectMultiLineItemResource.class, LineItem[].class, settings);

            if (shipBin.getDeliveryMethod().equals(DeliveryMethod.RetailPickup)) {
                parameters.add(retailerParameter);
            } else if (shipBin.getDeliveryMethod().equals(DeliveryMethod.ShipToStore)) {
                parameters.add(retailerParameter);
            } else {
                parameters.add(shippingAddressId);
                parameters.add(deliveryOption);
            }

            LineItem[] lis = multiLineItemResource.get(project.get_id(),
                    parameters.toArray(new Parameter[parameters.size()]));

//            if (lis != null && lis.length > 0)
//                newLineItems.addAll(Arrays.asList(lis));
//            else
//                logInvalidProjectError(project.get_id(), order.get_id());
            
            if (lis != null && lis.length > 0) {
                for (LineItem li : lis) {
                	if(settings.getContext().equals(sflyContext))
                    setProjectSpvData(li, project, lineItemSpvMap, settings);
                    newLineItems.add(li);
                }
            } else {
                logInvalidProjectError(project.get_id(), order.get_id());
            }
        }
        
        if (!lineItemSpvMap.isEmpty() && settings.getContext().equals(sflyContext)) {
            Map<String, CatalogDesign.EntityModel> designCatalogData = fetchDesignCatalogDataForAllSpvIds(settings, lineItemSpvMap);
            updateLineItemsWithCatalogData(newLineItems, designCatalogData);
        }
        
        return newLineItems;
    }
    
    private static void setProjectSpvData(LineItem lineItem, Project project, Map<Long, String> lineItemSpvMap, RequestSettings settings) {
        for (ProjectMetadata metadata : project.getProjectMetadata()) {
            if ("spvId".equals(metadata.getName())) {
                String spvId = String.valueOf(metadata.getValue());
                if (spvId != null && !spvId.trim().isEmpty()) {
                    lineItem.setSpvId(spvId);
                    lineItemSpvMap.put(lineItem.getLineItemId(), spvId);
                }
            }
            
            if ("designGroupId".equals(metadata.getName())) {
                String designGroupId = String.valueOf(metadata.getValue());
                if (designGroupId != null && !designGroupId.trim().isEmpty()) {
                    addMetadataToLineItem(lineItem, "designGroupId", designGroupId);
                }
            }
        }
        lineItem.setSourceGroup(settings.getSourceGroup());
    }
    
    private static void addMetadataToLineItem(LineItem lineItem, String key, String value) {
        if (lineItem.getMetadata() == null) {
            lineItem.setMetadata(new ArrayList<>());
        }

        if (lineItem.getMetadata().stream().noneMatch(meta -> key.equals(meta.getName()))) {
            Metadata metadataEntry = new Metadata();
            metadataEntry.setName(key);
            metadataEntry.setValue(value);
            metadataEntry.setMetadataType(AttributeType.Cart);
            lineItem.getMetadata().add(metadataEntry);
        }

        if (lineItem.getLineItemOptions() != null) {
            for (LineItemOption option : lineItem.getLineItemOptions()) {
                if (option.getMetadata() == null) {
                    option.setMetadata(new ArrayList<>());
                }

                if (option.getMetadata().stream().noneMatch(meta -> key.equals(meta.getName()))) {
                    Metadata optionMetadata = new Metadata();
                    optionMetadata.setName(key);
                    optionMetadata.setValue(value);
                    optionMetadata.setMetadataType(AttributeType.Cart);
                    option.getMetadata().add(optionMetadata);
                }
            }
        }
    }

    
    private static Map<String, CatalogDesign.EntityModel> fetchDesignCatalogDataForAllSpvIds(RequestSettings settings, Map<Long, String> lineItemSpvMap) {
        String sourceGroup = settings.getSourceGroup();
        String channel = "web"; 
        if (sourceGroup != null) {
            switch (sourceGroup.toLowerCase()) {
                case "web":
                    channel = "web";
                    break;
                case "android":
                    channel = "android";
                    break;
                case "ios":
                    channel = "ios";
                    break;
            }
        }
        String path = "/v1/sellableproductvariant?channel=" + channel + "&context=" + settings.getContext()
                + "&fl=designerCode,logicalProductTypeId,spvId";

        String catalogApiUrl = new DiscoveryFactory().getCatalogDesignUrl(settings.getContext(), settings.getDataCenter(), path);
        String payload = getJsonString(Collections.singletonMap("spvIds", new HashSet<>(lineItemSpvMap.values())));

        Map<String, CatalogDesign.EntityModel> designDataMap = new HashMap<>();

        try {
            CatalogDesign response = new OAuthHttpClient().doHttpPostRequest(catalogApiUrl, null, payload, CatalogDesign.class);

            if (response != null && response.getEntityModelList() != null) {
                for (CatalogDesign.EntityModel entityModel : response.getEntityModelList()) {
                    if (entityModel.getSpvId() != null) {
                        designDataMap.put(entityModel.getSpvId(), entityModel);
                    }
                }
            }
        } catch (Exception e) {
            SPLUNK.log("fetchDesignCatalogDataForAllSpvIds", Splunk.pair("error", e.getMessage()));
        }

        return designDataMap;
    }

    private static void updateLineItemsWithCatalogData(List<LineItem> lineItems, Map<String, CatalogDesign.EntityModel> designDataMap) {
        for (LineItem lineItem : lineItems) {
            if (lineItem.getSpvId() != null && designDataMap.containsKey(lineItem.getSpvId())) {
                CatalogDesign.EntityModel entityModel = designDataMap.get(lineItem.getSpvId());

                lineItem.setLogicalProductTypeId(entityModel.getLogicalProductTypeId());
                lineItem.setDesignerCode(entityModel.getDesignerCode());

                if (lineItem.getLineItemOptions() != null) {
                    for (LineItemOption option : lineItem.getLineItemOptions()) {
                        option.setLogicalProductTypeId(entityModel.getLogicalProductTypeId());
                        option.setDesignerCode(entityModel.getDesignerCode());
                    }
                }
            }
        }
    }
    
    private static void updateLineItemsWithMissingMetadata(RequestSettings settings, List<LineItem> clientLineItems, Project project) {
        Map<Long, String> lineItemSpvMap = new HashMap<>();

        for (LineItem lineItem : clientLineItems) {
            boolean missingSpv = (lineItem.getSpvId() == null || lineItem.getSpvId().trim().isEmpty());
            boolean missingDesignGroup = (lineItem.getMetadata() == null ||
                    lineItem.getMetadata().stream().noneMatch(meta -> "designGroupId".equals(meta.getName())));

            if (missingSpv || missingDesignGroup) {
                for (ProjectMetadata metadata : project.getProjectMetadata()) {
                    if ("spvId".equals(metadata.getName()) && metadata.getValue() != null) {
                        lineItem.setSpvId(metadata.getValue().toString());
                        lineItemSpvMap.put(lineItem.getLineItemId(), metadata.getValue().toString());
                    }
                    if ("designGroupId".equals(metadata.getName()) && metadata.getValue() != null) {
                        addMetadataToLineItem(lineItem, "designGroupId", metadata.getValue().toString());
                    }
                }
            }
            lineItem.setSourceGroup(settings.getSourceGroup());
        }

        // Batch fetch catalog data if needed
        if (!lineItemSpvMap.isEmpty()) {
            Map<String, CatalogDesign.EntityModel> designCatalogData = fetchDesignCatalogDataForAllSpvIds(settings, lineItemSpvMap);
            updateLineItemsWithCatalogData(clientLineItems, designCatalogData);
        }
    }


    private static String getJsonString(Object object) {
        try {
            return new CommerceObjectMapper().writeValueAsString(object);
        } catch (Exception e) {
            throw new RuntimeException("Unable to parse request/response to JSON", e);
        }
    }

}
